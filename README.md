# سپهروب - وبسایت شرکت طراحی و توسعه نرم‌افزار

یک وبسایت مدرن و کاملاً ریسپانسیو برای شرکت طراحی وبسایت و توسعه نرم‌افزار که با Next.js 15 و Tailwind CSS ساخته شده است.

## ✨ ویژگی‌های کلیدی

### 🎨 طراحی مدرن و موبایل فرست
- طراحی کاملاً ریسپانسیو برای تمام دستگاه‌ها
- رنگ‌بندی گرادیانت زیبا (آبی تا بنفش)
- انیمیشن‌های نرم و تعاملی
- UI/UX بهینه شده برای تجربه کاربری عالی

### 📱 صفحات کامل
1. **صفحه اصلی** - معرفی خدمات و CTA قدرتمند
2. **وبلاگ** - نمایش مقالات با طراحی کارتی
3. **نمونه کارها** - گالری پروژه‌ها با فیلتر دسته‌بندی
4. **پرسش و پاسخ** - آکاردئون تعاملی برای سوالات متداول
5. **تماس با ما** - فرم تماس + اطلاعات تماس
6. **درباره ما** - معرفی تیم، ارزش‌ها و آمار
7. **ورود/عضویت** - فرم‌های تعاملی با تب‌ها

### 🚀 مودال ثبت سفارش پیشرفته
- **انیمیشن نرم**: باز و بسته شدن با انیمیشن scale و blur
- **فیلدهای کامل**:
  - نام و نام خانوادگی (در یک ردیف)
  - شماره تماس با آیکون پرچم ایران
  - انتخاب نوع درخواست با توضیحات
  - توضیحات پروژه با شمارنده کارکتر هوشمند
- **ولیدیشن کامل**: بررسی تمام فیلدها با پیام‌های خطای واضح
- **Toast زیبا**: اعلان موفقیت با انیمیشن
- **API Integration**: ارسال به endpoint مخصوص

### 🛠️ تکنولوژی‌های استفاده شده
- **Next.js 15** - فریمورک React پیشرفته
- **TypeScript** - تایپ‌سیف بودن کد
- **Tailwind CSS** - استایل‌دهی مدرن و سریع
- **React Hooks** - مدیریت state و lifecycle
- **API Routes** - پردازش درخواست‌های سمت سرور

## 🚀 نحوه اجرا

```bash
# نصب وابستگی‌ها
npm install

# اجرای پروژه در حالت توسعه
npm run dev

# ساخت نسخه تولید
npm run build

# اجرای نسخه تولید
npm start
```

پروژه در آدرس [http://localhost:3002](http://localhost:3002) در دسترس خواهد بود.

## 📞 اطلاعات تماس

- **تلفن**: ۰۹۳۹۷۱۹۲۲۳۰
- **ایمیل**: <EMAIL>

---

**ساخته شده با ❤️ توسط تیم سپهروب**
