'use client';

import Link from "next/link";
import { useState } from "react";
import OrderModal from "../components/OrderModal";

export default function Home() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const services = [
    {
      icon: "🌐",
      title: "طراحی وبسایت",
      description: "طراحی وبسایت‌های مدرن و ریسپانسیو با بهترین تکنولوژی‌ها",
      features: ["ریسپانسیو", "سئو شده", "سرعت بالا", "امن"]
    },
    {
      icon: "📱",
      title: "اپلیکیشن موبایل",
      description: "توسعه اپلیکیشن‌های اندروید و iOS با عملکرد بهینه",
      features: ["کراس پلتفرم", "UI/UX مدرن", "عملکرد بالا", "پشتیبانی"]
    },
    {
      icon: "💻",
      title: "نرم‌افزار دسکتاپ",
      description: "ساخت نرم‌افزارهای دسکتاپ قدرتمند و کاربرپسند",
      features: ["چند پلتفرم", "رابط زیبا", "قابل اعتماد", "به‌روزرسانی"]
    },
    {
      icon: "🎨",
      title: "طراحی UI/UX",
      description: "طراحی رابط کاربری زیبا و تجربه کاربری بهینه",
      features: ["طراحی مدرن", "کاربرپسند", "تست شده", "استاندارد"]
    },
    {
      icon: "🛒",
      title: "فروشگاه آنلاین",
      description: "راه‌اندازی فروشگاه‌های آنلاین کامل با درگاه پرداخت",
      features: ["درگاه پرداخت", "مدیریت محصول", "پنل کاربری", "گزارش‌گیری"]
    },
    {
      icon: "⚙️",
      title: "سیستم‌های مدیریت",
      description: "توسعه سیستم‌های مدیریت اختصاصی برای کسب‌وکارها",
      features: ["سفارشی‌سازی", "امنیت بالا", "مقیاس‌پذیر", "پشتیبانی ۲۴/۷"]
    }
  ];

  const stats = [
    { number: "۲۰۰+", label: "پروژه موفق" },
    { number: "۱۵۰+", label: "مشتری راضی" },
    { number: "15+", label: "سال تجربه" },
    { number: "۲۴/۷", label: "پشتیبانی" }
  ];

  return (
    <main className="bg-white md:min-h-screen">
      {/* Navigation */}
      <nav className="bg-white  sticky top-0 z-40">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">س</span>
              </div>
              <span className="text-xl font-bold text-gray-900 mr-2">سپهروب</span>
            </Link>

            {/* Desktop Menu */}
            <div className="hidden md:flex items-center gap-8">
              <Link href="/" className="text-blue-600 font-medium">صفحه اصلی</Link>
              <Link href="/blog" className="text-gray-700 hover:text-blue-600 transition-colors">وبلاگ</Link>
              <Link href="/portfolio" className="text-gray-700 hover:text-blue-600 transition-colors">نمونه کارها</Link>
              <Link href="/faq" className="text-gray-700 hover:text-blue-600 transition-colors">پرسش و پاسخ</Link>
              <Link href="/contact" className="text-gray-700 hover:text-blue-600 transition-colors">تماس با ما</Link>
              <Link href="/about" className="text-gray-700 hover:text-blue-600 transition-colors">درباره ما</Link>
            </div>

            {/* CTA Button */}
            <div className="hidden md:flex items-center space-x-4 space-x-reverse">
              <Link href="/login" className=" bg-blue-600 rounded-3xl px-3 py-2 text-white hover:bg-blue-500 text-sm transition-colors">ورود / عضویت</Link>
              
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="md:hidden p-2"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>

          {/* Mobile Menu */}
          {mobileMenuOpen && (
            <div className="md:hidden py-4 border-t">
              <div className="flex flex-col space-y-4">
                <Link href="/" className="text-blue-600 font-medium">صفحه اصلی</Link>
                <Link href="/blog" className="text-gray-700">وبلاگ</Link>
                <Link href="/portfolio" className="text-gray-700">نمونه کارها</Link>
                <Link href="/faq" className="text-gray-700">پرسش و پاسخ</Link>
                <Link href="/contact" className="text-gray-700">تماس با ما</Link>
                <Link href="/about" className="text-gray-700">درباره ما</Link>
                <div className="pt-4 border-t">
                  <button
                    onClick={() => setIsModalOpen(true)}
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-full"
                  >
                    شروع پروژه
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 via-white to-purple-50 py-12 md:py-20">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Content */}
            <div className="text-center lg:text-right">
              <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                طراحی وبسایت و
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600"> نرم‌افزار اختصاصی</span>
              </h1>
              <p className="text-base text-gray-600 mb-8 leading-relaxed pb-16">
                ما با بیش از 20 سال تجربه، بهترین راه‌حل‌های دیجیتال را برای کسب‌وکار شما ارائه می‌دهیم.
                از طراحی وبسایت تا توسعه اپلیکیشن موبایل، همه چیز در یک مکان.
              </p>

              <div className="flex gap-4 justify-center lg:justify-start mb-8">
                <button
                type="button"
                  onClick={() => setIsModalOpen(true)}
                  className="bg-gradient-to-r text-sm from-blue-600 to-purple-600 cursor-pointer text-white px-8 py-2.5 rounded-full  font-medium hover:shadow-xl transition-all duration-300 transform "
                >
                   ثبت سفارش
                </button>
                <Link
                  href="/portfolio"
                  className="border-2 border-gray-300 text-gray-700 px-8 py-2.5 rounded-full text-sm font-medium hover:border-blue-600 hover:text-blue-600 transition-all duration-300"
                >
                   مشاهده نمونه کارها
                </Link>
              </div>

              {/* Contact Info */}
              <div className="flex  items-center justify-center lg:justify-start gap-6 text-gray-600">
                <Link href="tel:09397192230" className="flex items-center gap-2 hover:text-blue-600 transition-colors">
                  <div className="w-10 h-10  rounded-full flex items-center justify-center">
                    <svg className="size-8 max-md:size-6 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                  </div>
                  <span className="font-medium">۰۹۳۹۷۱۹۲۲۳۰</span>
                </Link>
                <div className="flex items-center gap-2">
                  <div className="w-10 h-10 rounded-full flex items-center justify-center">
                    <svg className="size-8 max-md:size-6 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <span>مشاوره رایگان ۲۴/۷</span>
                </div>
              </div>
            </div>

            {/* Visual */}
            <div className="relative max-md:hidden">
              <div className="relative z-10 bg-white rounded-2xl shadow-2xl p-8 md:max-w-xl h-96">
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  </div>
                  <div className="space-y-3">
                    <div className="h-4 bg-gradient-to-r from-blue-200 to-purple-200 rounded"></div>
                    <div className="h-4 bg-gradient-to-r from-purple-200 to-pink-200 rounded w-3/4"></div>
                    <div className="h-4 bg-gradient-to-r from-green-200 to-blue-200 rounded w-1/2"></div>
                  </div>
                  <div className="grid grid-cols-2 gap-4 mt-6">
                    <div className="h-20 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg"></div>
                    <div className="h-20 bg-gradient-to-br from-purple-100 to-purple-200 rounded-lg"></div>
                  </div>
                </div>
              </div>

              {/* Background Elements */}
              <div className="absolute -top-4 -right-4 w-20 h-20 bg-blue-200 rounded-full opacity-60"></div>
              <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-purple-200 rounded-full opacity-60"></div>
              <div className="absolute top-1/2 -left-8 w-12 h-12 bg-pink-200 rounded-full opacity-40"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-2xl md:text-3xl font-bold text-blue-600 mb-2">{stat.number}</div>
                <div className="text-gray-600 text-sm md:text-base">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 md:py-24 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              خدمات ما
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              ما طیف کاملی از خدمات دیجیتال را ارائه می‌دهیم تا کسب‌وکار شما را به سطح بعدی برسانیم
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <div key={index} className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div className="text-4xl mb-4">{service.icon}</div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">{service.title}</h3>
                <p className="text-gray-600 mb-6 leading-relaxed">{service.description}</p>
                <div className="space-y-2">
                  {service.features.map((feature, idx) => (
                    <div key={idx} className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                      <span className="text-sm text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>
                <button
                  onClick={() => setIsModalOpen(true)}
                  className="mt-6 w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-full hover:shadow-lg transition-all duration-300"
                >
                  درخواست قیمت
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-16 md:py-24 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                چرا سپهروب را انتخاب کنید؟
              </h2>
              <div className="space-y-6">
                <div className="flex gap-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">تکنولوژی روز دنیا</h3>
                    <p className="text-gray-600">استفاده از جدیدترین تکنولوژی‌ها و فریمورک‌های مدرن برای بهترین عملکرد</p>
                  </div>
                </div>
                <div className="flex gap-4">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">تحویل به موقع</h3>
                    <p className="text-gray-600">تعهد به زمان‌بندی پروژه و تحویل در موعد مقرر با حفظ کیفیت بالا</p>
                  </div>
                </div>
                <div className="flex gap-4">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 100 19.5 9.75 9.75 0 000-19.5z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">پشتیبانی ۲۴/۷</h3>
                    <p className="text-gray-600">پشتیبانی کامل پس از تحویل پروژه و رفع مشکلات در کمترین زمان</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="relative">
              <div className="bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl p-8">
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-white rounded-xl p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600">۱۰۰%</div>
                    <div className="text-sm text-gray-600">رضایت مشتری</div>
                  </div>
                  <div className="bg-white rounded-xl p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">۲۴/۷</div>
                    <div className="text-sm text-gray-600">پشتیبانی</div>
                  </div>
                  <div className="bg-white rounded-xl p-4 text-center">
                    <div className="text-2xl font-bold text-purple-600">۵+</div>
                    <div className="text-sm text-gray-600">سال تجربه</div>
                  </div>
                  <div className="bg-white rounded-xl p-4 text-center">
                    <div className="text-2xl font-bold text-orange-600">۲۰۰+</div>
                    <div className="text-sm text-gray-600">پروژه موفق</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            آماده شروع پروژه هستید؟
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            همین الان با ما تماس بگیرید و مشاوره رایگان دریافت کنید
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => setIsModalOpen(true)}
              className="bg-white text-blue-600 px-8 py-2.5 rounded-full text-lg font-medium hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
             ثبت سفارش
            </button>
            <Link
              href="tel:09397192230"
              className="border-2 border-white text-white px-8 py-2.5 rounded-full text-lg font-medium hover:bg-white hover:text-blue-600 transition-all duration-300"
            >
               تماس فوری
            </Link>
          </div>
        </div>
      </section>

      {/* Order Modal */}
      <OrderModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </main>
  );
}
