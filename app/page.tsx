'use client';

import Link from "next/link";
import { useState } from "react";
import OrderModal from "../components/OrderModal";

export default function Home() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const services = [
    {
      icon: "🌐",
      title: "طراحی وبسایت",
      description: "طراحی وبسایت‌های مدرن و ریسپانسیو با بهترین تکنولوژی‌ها",
      features: ["ریسپانسیو", "سئو شده", "سرعت بالا", "امن"]
    },
    {
      icon: "📱",
      title: "اپلیکیشن موبایل",
      description: "توسعه اپلیکیشن‌های اندروید و iOS با عملکرد بهینه",
      features: ["کراس پلتفرم", "UI/UX مدرن", "عملکرد بالا", "پشتیبانی"]
    },
    {
      icon: "💻",
      title: "نرم‌افزار دسکتاپ",
      description: "ساخت نرم‌افزارهای دسکتاپ قدرتمند و کاربرپسند",
      features: ["چند پلتفرم", "رابط زیبا", "قابل اعتماد", "به‌روزرسانی"]
    },
    {
      icon: "🎨",
      title: "طراحی UI/UX",
      description: "طراحی رابط کاربری زیبا و تجربه کاربری بهینه",
      features: ["طراحی مدرن", "کاربرپسند", "تست شده", "استاندارد"]
    },
    {
      icon: "🛒",
      title: "فروشگاه آنلاین",
      description: "راه‌اندازی فروشگاه‌های آنلاین کامل با درگاه پرداخت",
      features: ["درگاه پرداخت", "مدیریت محصول", "پنل کاربری", "گزارش‌گیری"]
    },
    {
      icon: "⚙️",
      title: "سیستم‌های مدیریت",
      description: "توسعه سیستم‌های مدیریت اختصاصی برای کسب‌وکارها",
      features: ["سفارشی‌سازی", "امنیت بالا", "مقیاس‌پذیر", "پشتیبانی ۲۴/۷"]
    }
  ];

  const stats = [
    { number: "۲۰۰+", label: "پروژه موفق" },
    { number: "۱۵۰+", label: "مشتری راضی" },
    { number: "15+", label: "سال تجربه" },
    { number: "۲۴/۷", label: "پشتیبانی" }
  ];

  return (
    <main className="bg-white md:min-h-screen">
      {/* Navigation */}
      <nav className="bg-white  sticky top-0 z-40">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">س</span>
              </div>
              <span className="text-xl font-bold text-gray-900 mr-2">سپهروب</span>
            </Link>

            {/* Desktop Menu */}
            <div className="hidden md:flex items-center gap-8">
              <Link href="/" className="text-blue-600 font-medium">صفحه اصلی</Link>
              <Link href="/blog" className="text-gray-700 hover:text-blue-600 transition-colors">وبلاگ</Link>
              <Link href="/portfolio" className="text-gray-700 hover:text-blue-600 transition-colors">نمونه کارها</Link>
              <Link href="/faq" className="text-gray-700 hover:text-blue-600 transition-colors">پرسش و پاسخ</Link>
              <Link href="/contact" className="text-gray-700 hover:text-blue-600 transition-colors">تماس با ما</Link>
              <Link href="/about" className="text-gray-700 hover:text-blue-600 transition-colors">درباره ما</Link>
            </div>

            {/* CTA Button */}
            <div className="hidden md:flex items-center space-x-4 space-x-reverse">
              <Link href="/login" className=" bg-blue-600 rounded-3xl px-3 py-2 text-white hover:bg-blue-500 text-sm transition-colors">ورود / عضویت</Link>
              
            </div>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="md:hidden p-2"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>

          {/* Mobile Menu Overlay */}
          {mobileMenuOpen && (
            <div className="fixed inset-0 z-50 md:hidden">
              {/* Backdrop */}
              <div
                className="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
                onClick={() => setMobileMenuOpen(false)}
              ></div>

              {/* Slide Menu */}
              <div className={`absolute top-0 right-0 h-full w-80 bg-white shadow-2xl transform transition-transform duration-300 ease-out ${
                mobileMenuOpen ? 'translate-x-0' : 'translate-x-full'
              }`}>
                {/* Header */}
                <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6 text-white">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-xl font-bold">سپهروب</h3>
                      <p className="text-blue-100 text-sm">طراحی و توسعه نرم‌افزار</p>
                    </div>
                    <button
                      onClick={() => setMobileMenuOpen(false)}
                      className="p-2 hover:bg-white hover:bg-opacity-20 rounded-full transition-colors"
                    >
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>
                </div>

                {/* Menu Items */}
                <div className="p-6 space-y-1">
                  <Link
                    href="/"
                    className="flex items-center gap-3 py-3 px-4 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-xl transition-all"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <span className="text-xl">🏠</span>
                    <span className="font-medium">صفحه اصلی</span>
                  </Link>

                  <Link
                    href="/portfolio"
                    className="flex items-center gap-3 py-3 px-4 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-xl transition-all"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <span className="text-xl">💼</span>
                    <span className="font-medium">نمونه کارها</span>
                  </Link>

                  <Link
                    href="/blog"
                    className="flex items-center gap-3 py-3 px-4 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-xl transition-all"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <span className="text-xl">📝</span>
                    <span className="font-medium">وبلاگ</span>
                  </Link>

                  <Link
                    href="/faq"
                    className="flex items-center gap-3 py-3 px-4 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-xl transition-all"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <span className="text-xl">❓</span>
                    <span className="font-medium">پرسش و پاسخ</span>
                  </Link>

                  <Link
                    href="/contact"
                    className="flex items-center gap-3 py-3 px-4 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-xl transition-all"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <span className="text-xl">📞</span>
                    <span className="font-medium">تماس با ما</span>
                  </Link>

                  <Link
                    href="/about"
                    className="flex items-center gap-3 py-3 px-4 text-gray-700 hover:bg-blue-50 hover:text-blue-600 rounded-xl transition-all"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <span className="text-xl">ℹ️</span>
                    <span className="font-medium">درباره ما</span>
                  </Link>

                  <div className="border-t border-gray-200 my-4"></div>

                  <button
                    onClick={() => {
                      setIsModalOpen(true);
                      setMobileMenuOpen(false);
                    }}
                    className="w-full flex items-center gap-3 py-3 px-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:shadow-lg transition-all"
                  >
                    <span className="text-xl">🚀</span>
                    <span className="font-medium">ثبت سفارش پروژه</span>
                  </button>

                  <Link
                    href="/login"
                    className="flex items-center gap-3 py-3 px-4 border-2 border-blue-600 text-blue-600 rounded-xl hover:bg-blue-600 hover:text-white transition-all"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <span className="text-xl">👤</span>
                    <span className="font-medium">ورود / عضویت</span>
                  </Link>
                </div>

                {/* Contact Info */}
                <div className="absolute bottom-0 left-0 right-0 p-6 bg-gray-50 border-t">
                  <div className="text-center">
                    <p className="text-sm text-gray-600 mb-2">تماس فوری</p>
                    <Link
                      href="tel:09397192230"
                      className="text-lg font-bold text-blue-600 hover:text-blue-700"
                    >
                      ۰۹۳۹۷۱۹۲۲۳۰
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative min-h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 overflow-hidden">
        {/* Animated Background with SVG Patterns */}
        <div className="absolute inset-0">
          {/* Floating SVG Icons */}
          <div className="absolute top-20 left-20 animate-float">
            <svg className="w-24 h-24 text-blue-400 opacity-30" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
            </svg>
          </div>

          <div className="absolute top-40 right-20 animate-pulse animation-delay-2000">
            <svg className="w-32 h-32 text-purple-400 opacity-20" fill="currentColor" viewBox="0 0 24 24">
              <path d="M9.4 16.6L4.8 12l4.6-4.6L8 6l-6 6 6 6 1.4-1.4zm5.2 0L19.2 12l-4.6-4.6L16 6l6 6-6 6-1.4-1.4z"/>
            </svg>
          </div>

          <div className="absolute bottom-20 left-1/3 animate-bounce animation-delay-4000">
            <svg className="w-20 h-20 text-pink-400 opacity-25" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
          </div>

          {/* Geometric Shapes */}
          <div className="absolute top-1/4 right-1/4 w-4 h-4 bg-cyan-400 rotate-45 animate-spin opacity-60" style={{animationDuration: '8s'}}></div>
          <div className="absolute bottom-1/3 left-1/4 w-6 h-6 bg-yellow-400 rounded-full animate-pulse opacity-50"></div>
          <div className="absolute top-1/2 right-1/3 w-3 h-3 bg-green-400 animate-ping opacity-40"></div>

          {/* Grid Pattern */}
          <div className="absolute inset-0 opacity-5">
            <svg className="w-full h-full" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                  <path d="M 40 0 L 0 0 0 40" fill="none" stroke="white" strokeWidth="1"/>
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#grid)" />
            </svg>
          </div>
        </div>

        <div className="relative z-10 container mx-auto px-4 py-20 lg:py-32">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Content */}
            <div className="text-center lg:text-right">
              {/* Badge */}
              <div className="inline-flex items-center gap-3 bg-white bg-opacity-10 backdrop-blur-sm border border-white border-opacity-20 rounded-full px-6 py-3 mb-8">
                <svg className="w-5 h-5 text-green-400 animate-pulse" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                </svg>
                <span className="text-white font-medium">آنلاین و آماده خدمات‌رسانی</span>
                <svg className="w-4 h-4 text-yellow-400 animate-spin" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              </div>

              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-8 leading-tight">
                <div className="flex items-center justify-center lg:justify-start gap-4 mb-4">
                  <svg className="w-16 h-16 text-yellow-400 animate-bounce" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 11H7v3h2v-3zm4 0h-2v3h2v-3zm4 0h-2v3h2v-3zm2-7h-1V2h-2v2H8V2H6v2H5c-1.1 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11z"/>
                  </svg>
                  آینده کسب‌وکار شما
                </div>
                <div className="flex items-center justify-center lg:justify-start gap-4">
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400">
                    از امروز شروع می‌شود
                  </span>
                  <svg className="w-12 h-12 text-pink-400 animate-pulse" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                  </svg>
                </div>
              </h1>

              <p className="text-xl md:text-2xl text-blue-100 mb-8 leading-relaxed">
                با تیم متخصص سپهروب، وبسایت و اپلیکیشن‌های حرفه‌ای بسازید که مشتریان شما را شگفت‌زده کند
              </p>

              {/* Features */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mb-10">
                <div className="group bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 border border-white border-opacity-20 hover:bg-opacity-20 transition-all duration-300 transform hover:-translate-y-2">
                  <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl mb-4 group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M13 2.05v3.03c3.39.49 6 3.39 6 6.92 0 .9-.18 1.75-.48 2.54l2.6 1.53c.56-1.24.88-2.62.88-4.07 0-5.18-3.95-9.45-9-9.95zM12 19c-3.87 0-7-3.13-7-7 0-3.53 2.61-6.43 6-6.92V2.05c-5.06.5-9 4.76-9 9.95 0 5.52 4.47 10 9.99 10 3.31 0 6.24-1.61 8.06-4.09l-2.6-1.53C16.17 17.98 14.21 19 12 19z"/>
                    </svg>
                  </div>
                  <div className="text-white font-bold text-lg mb-2">سرعت برق‌آسا</div>
                  <div className="text-blue-200">تحویل در کمترین زمان ممکن</div>
                </div>

                <div className="group bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 border border-white border-opacity-20 hover:bg-opacity-20 transition-all duration-300 transform hover:-translate-y-2">
                  <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-400 to-blue-500 rounded-2xl mb-4 group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                  </div>
                  <div className="text-white font-bold text-lg mb-2">کیفیت پریمیوم</div>
                  <div className="text-blue-200">استاندارد بین‌المللی</div>
                </div>

                <div className="group bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 border border-white border-opacity-20 hover:bg-opacity-20 transition-all duration-300 transform hover:-translate-y-2">
                  <div className="flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-400 to-pink-500 rounded-2xl mb-4 group-hover:scale-110 transition-transform duration-300">
                    <svg className="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>
                    </svg>
                  </div>
                  <div className="text-white font-bold text-lg mb-2">پشتیبانی ۲۴/۷</div>
                  <div className="text-blue-200">همیشه در کنار شما</div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-8">
                <button
                  type="button"
                  onClick={() => setIsModalOpen(true)}
                  className="group relative bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-4 rounded-full text-lg font-bold hover:shadow-2xl transition-all duration-300 transform hover:scale-105 overflow-hidden"
                >
                  <span className="relative z-10 flex items-center justify-center gap-2">
                    🚀 شروع پروژه رایگان
                  </span>
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-pink-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </button>

                <Link
                  href="/portfolio"
                  className="group border-2 border-white border-opacity-30 text-white px-8 py-4 rounded-full text-lg font-medium hover:bg-white hover:text-gray-900 transition-all duration-300 backdrop-blur-sm"
                >
                  <span className="flex items-center justify-center gap-2">
                    👁️ نمونه کارها
                    <svg className="w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                  </span>
                </Link>
              </div>

              {/* Contact Info */}
              <div className="flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-6">
                <Link
                  href="tel:09397192230"
                  className="group flex items-center gap-3 bg-white bg-opacity-10 backdrop-blur-sm border border-white border-opacity-20 rounded-full px-6 py-3 text-white hover:bg-white hover:text-gray-900 transition-all duration-300"
                >
                  <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center group-hover:bg-blue-600 transition-colors">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                  </div>
                  <div>
                    <div className="font-bold">۰۹۳۹۷۱۹۲۲۳۰</div>
                    <div className="text-sm opacity-75 group-hover:opacity-100">تماس فوری</div>
                  </div>
                </Link>

                <div className="flex items-center gap-3 text-blue-200">
                  <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <div className="font-medium text-white">مشاوره رایگان</div>
                    <div className="text-sm">۲۴ ساعته</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Visual */}
            <div className="relative lg:block hidden">
              {/* Main Device */}
              <div className="relative z-10 bg-white rounded-3xl shadow-2xl p-8 transform rotate-3 hover:rotate-0 transition-transform duration-500">
                {/* Browser Header */}
                <div className="flex items-center gap-2 mb-6">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <div className="flex-1 bg-gray-100 rounded-full h-6 mx-4"></div>
                </div>

                {/* Content */}
                <div className="space-y-4">
                  <div className="h-6 bg-gradient-to-r from-blue-400 to-purple-500 rounded-lg"></div>
                  <div className="h-4 bg-gradient-to-r from-purple-300 to-pink-400 rounded w-3/4"></div>
                  <div className="h-4 bg-gradient-to-r from-green-300 to-blue-400 rounded w-1/2"></div>

                  <div className="grid grid-cols-2 gap-4 mt-8">
                    <div className="h-24 bg-gradient-to-br from-blue-100 to-blue-300 rounded-xl flex items-center justify-center">
                      <span className="text-2xl">📱</span>
                    </div>
                    <div className="h-24 bg-gradient-to-br from-purple-100 to-purple-300 rounded-xl flex items-center justify-center">
                      <span className="text-2xl">💻</span>
                    </div>
                  </div>

                  <div className="h-16 bg-gradient-to-r from-pink-200 to-orange-300 rounded-xl flex items-center justify-center mt-4">
                    <span className="text-xl font-bold text-gray-700">سپهروب</span>
                  </div>
                </div>
              </div>

              {/* Floating Elements */}
              <div className="absolute -top-8 -right-8 w-16 h-16 bg-blue-400 rounded-2xl opacity-80 animate-bounce"></div>
              <div className="absolute -bottom-8 -left-8 w-20 h-20 bg-purple-400 rounded-full opacity-60 animate-pulse"></div>
              <div className="absolute top-1/2 -left-12 w-12 h-12 bg-pink-400 rounded-lg opacity-70 animate-spin" style={{animationDuration: '3s'}}></div>

              {/* Mobile Device */}
              <div className="absolute -bottom-4 -right-4 w-32 h-56 bg-gray-900 rounded-3xl p-2 shadow-xl transform -rotate-12 hover:rotate-0 transition-transform duration-500">
                <div className="w-full h-full bg-white rounded-2xl p-3">
                  <div className="w-full h-2 bg-gray-200 rounded mb-2"></div>
                  <div className="space-y-2">
                    <div className="h-3 bg-blue-300 rounded w-3/4"></div>
                    <div className="h-3 bg-purple-300 rounded w-1/2"></div>
                    <div className="h-8 bg-gradient-to-r from-blue-400 to-purple-500 rounded mt-4"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
          </svg>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gradient-to-r from-blue-50 to-purple-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              اعتماد بیش از هزاران مشتری
            </h2>
            <p className="text-lg text-gray-600">
              آمار موفقیت‌های ما در یک نگاه
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="group text-center bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
                  <span className="text-2xl text-white font-bold">
                    {index === 0 ? '📊' : index === 1 ? '😊' : index === 2 ? '⏱️' : '🏆'}
                  </span>
                </div>
                <div className="text-3xl md:text-4xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                  {stat.number}
                </div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Technologies Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              تکنولوژی‌های پیشرفته
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              از جدیدترین و قدرتمندترین تکنولوژی‌های دنیا برای ساخت پروژه شما استفاده می‌کنیم
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
            {[
              { name: 'React', icon: '⚛️', color: 'from-blue-400 to-cyan-400' },
              { name: 'Next.js', icon: '▲', color: 'from-gray-700 to-gray-900' },
              { name: 'Vue.js', icon: '💚', color: 'from-green-400 to-emerald-500' },
              { name: 'Laravel', icon: '🔥', color: 'from-red-500 to-orange-500' },
              { name: 'Node.js', icon: '🟢', color: 'from-green-500 to-lime-500' },
              { name: 'Python', icon: '🐍', color: 'from-blue-500 to-yellow-400' },
              { name: 'Flutter', icon: '📱', color: 'from-blue-400 to-blue-600' },
              { name: 'React Native', icon: '📲', color: 'from-purple-500 to-pink-500' },
              { name: 'WordPress', icon: '📝', color: 'from-blue-600 to-blue-800' },
              { name: 'Shopify', icon: '🛒', color: 'from-green-600 to-green-800' },
              { name: 'MongoDB', icon: '🍃', color: 'from-green-500 to-green-700' },
              { name: 'MySQL', icon: '🐬', color: 'from-orange-500 to-red-500' }
            ].map((tech, index) => (
              <div key={index} className="group text-center p-4 rounded-2xl hover:bg-gray-50 transition-all duration-300 transform hover:-translate-y-2">
                <div className={`w-16 h-16 bg-gradient-to-r ${tech.color} rounded-2xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                  <span className="text-2xl text-white">{tech.icon}</span>
                </div>
                <div className="text-sm font-medium text-gray-700 group-hover:text-gray-900">{tech.name}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-16 md:py-24 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              خدمات ما
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              ما طیف کاملی از خدمات دیجیتال را ارائه می‌دهیم تا کسب‌وکار شما را به سطح بعدی برسانیم
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <div key={index} className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div className="text-4xl mb-4">{service.icon}</div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">{service.title}</h3>
                <p className="text-gray-600 mb-6 leading-relaxed">{service.description}</p>
                <div className="space-y-2">
                  {service.features.map((feature, idx) => (
                    <div key={idx} className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                      <span className="text-sm text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>
                <button
                  onClick={() => setIsModalOpen(true)}
                  className="mt-6 w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-full hover:shadow-lg transition-all duration-300"
                >
                  درخواست قیمت
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16 bg-gradient-to-br from-blue-900 to-purple-900 text-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              نظرات مشتریان ما
            </h2>
            <p className="text-xl text-blue-100">
              تجربه واقعی کسانی که با ما کار کرده‌اند
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {[
              {
                name: 'احمد رضایی',
                role: 'مدیرعامل شرکت تجاری',
                text: 'سپهروب واقعاً کار فوق‌العاده‌ای انجام داد. وبسایت ما حالا بهترین در صنعت است.',
                rating: 5,
                avatar: '👨‍💼'
              },
              {
                name: 'فاطمه احمدی',
                role: 'صاحب فروشگاه آنلاین',
                text: 'اپلیکیشن موبایلی که برایم ساختند فروش ما را ۳ برابر کرد. عالی بودند!',
                rating: 5,
                avatar: '👩‍💻'
              },
              {
                name: 'محمد کریمی',
                role: 'استارتاپ فناوری',
                text: 'تیم حرفه‌ای و سریع. پروژه ما را زودتر از موعد تحویل دادند.',
                rating: 5,
                avatar: '👨‍🚀'
              }
            ].map((testimonial, index) => (
              <div key={index} className="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-6 border border-white border-opacity-20 hover:bg-opacity-20 transition-all duration-300">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-2xl">
                    {testimonial.avatar}
                  </div>
                  <div className="mr-4">
                    <div className="font-bold">{testimonial.name}</div>
                    <div className="text-blue-200 text-sm">{testimonial.role}</div>
                  </div>
                </div>
                <p className="text-blue-100 mb-4 leading-relaxed">"{testimonial.text}"</p>
                <div className="flex gap-1">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <span key={i} className="text-yellow-400 text-lg">⭐</span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-16 md:py-24 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                چرا سپهروب را انتخاب کنید؟
              </h2>
              <div className="space-y-6">
                <div className="flex gap-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">تکنولوژی روز دنیا</h3>
                    <p className="text-gray-600">استفاده از جدیدترین تکنولوژی‌ها و فریمورک‌های مدرن برای بهترین عملکرد</p>
                  </div>
                </div>
                <div className="flex gap-4">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">تحویل به موقع</h3>
                    <p className="text-gray-600">تعهد به زمان‌بندی پروژه و تحویل در موعد مقرر با حفظ کیفیت بالا</p>
                  </div>
                </div>
                <div className="flex gap-4">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 100 19.5 9.75 9.75 0 000-19.5z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">پشتیبانی ۲۴/۷</h3>
                    <p className="text-gray-600">پشتیبانی کامل پس از تحویل پروژه و رفع مشکلات در کمترین زمان</p>
                  </div>
                </div>
              </div>
            </div>
            <div className="relative">
              <div className="bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl p-8">
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-white rounded-xl p-4 text-center">
                    <div className="text-2xl font-bold text-blue-600">۱۰۰%</div>
                    <div className="text-sm text-gray-600">رضایت مشتری</div>
                  </div>
                  <div className="bg-white rounded-xl p-4 text-center">
                    <div className="text-2xl font-bold text-green-600">۲۴/۷</div>
                    <div className="text-sm text-gray-600">پشتیبانی</div>
                  </div>
                  <div className="bg-white rounded-xl p-4 text-center">
                    <div className="text-2xl font-bold text-purple-600">۵+</div>
                    <div className="text-sm text-gray-600">سال تجربه</div>
                  </div>
                  <div className="bg-white rounded-xl p-4 text-center">
                    <div className="text-2xl font-bold text-orange-600">۲۰۰+</div>
                    <div className="text-sm text-gray-600">پروژه موفق</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-purple-600">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            آماده شروع پروژه هستید؟
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            همین الان با ما تماس بگیرید و مشاوره رایگان دریافت کنید
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => setIsModalOpen(true)}
              className="bg-white text-blue-600 px-8 py-2.5 rounded-full text-lg font-medium hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
             ثبت سفارش
            </button>
            <Link
              href="tel:09397192230"
              className="border-2 border-white text-white px-8 py-2.5 rounded-full text-lg font-medium hover:bg-white hover:text-blue-600 transition-all duration-300"
            >
               تماس فوری
            </Link>
          </div>
        </div>
      </section>

      {/* Order Modal */}
      <OrderModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </main>
  );
}
