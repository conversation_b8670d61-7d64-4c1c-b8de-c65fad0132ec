'use client';

import Link from "next/link";
import { useState } from "react";
import OrderModal from "../components/OrderModal";

export default function Home() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  return (
    <main className="bg-white">
      <nav className="bg-gray-900 text-white">
        <div className="container mx-auto  flex items-center  p-5 justify-between ">
          <div>
            <h1 className="text-2xl font-bold">سپهروب</h1>
          </div>
          <ul className="flex items-center gap-8">
            <li>
              <Link href="/">صفحه اصلی</Link>
            </li>
            <li>
              <Link href="/">وبلاگ</Link>
            </li>
            <li>
              <Link href="/">پرسش و پاسخ</Link>
            </li>
            <li>
              <Link href="/">نمونه کارها</Link>
            </li>
            <li>
              <Link href="/">تماس با ما</Link>
            </li>
            <li>
              <Link href="/">درباره ما</Link>
            </li>
          </ul>
          <div >
            <Link href="/login" className="bg-blue-600 hover:bg-blue-500 transition-all px-4 py-1.5 text-sm text-white rounded-2xl">ورود / عضویت</Link>
          </div>
        </div>
      </nav>
      <div className="min-h-72 bg-gray-900 flex items-center justify-center flex-col gap-3  h-72">
        <h1 className="text-2xl text-white font-bold  ">طراحی وبسایت و نرم افزار اختصاصی</h1>
        <div className="mt-6 flex items-center gap-8">
          <button className="bg-blue-600 hover:bg-blue-500 transition-all px-6 py-2.5 text-sm text-white rounded-3xl">ثبت سفارش</button>
          <Link href="tel:09397192230" className="text-base text-white">
            <div className="flex items-center gap-2 flex-row-reverse">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" className="size-6">
                <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 1.5H8.25A2.25 2.25 0 0 0 6 3.75v16.5a2.25 2.25 0 0 0 2.25 2.25h7.5A2.25 2.25 0 0 0 18 20.25V3.75a2.25 2.25 0 0 0-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3" />
              </svg>

              <span>09397192230</span>
            </div>
          </Link>
        </div>
      </div>
    </main>
  );
}
