import Link from "next/link";

export default function Portfolio() {
  const projects = [
    {
      title: "فروشگاه آنلاین مد و پوشاک",
      category: "فروشگاه آنلاین",
      image: "bg-gradient-to-r from-pink-500 to-rose-500",
      description: "طراحی و توسعه فروشگاه آنلاین کامل با پنل مدیریت و درگاه پرداخت",
      technologies: ["React", "Node.js", "MongoDB", "Stripe"],
      link: "#"
    },
    {
      title: "سیستم مدیریت بیمارستان",
      category: "نرم‌افزار مدیریت",
      image: "bg-gradient-to-r from-blue-500 to-cyan-500",
      description: "سیستم جامع مدیریت بیمارستان شامل مدیریت بیماران، پرسنل و دارو",
      technologies: ["Vue.js", "Laravel", "MySQL", "Chart.js"],
      link: "#"
    },
    {
      title: "اپلیکیشن موبایل رستوران",
      category: "اپلیکیشن موبایل",
      image: "bg-gradient-to-r from-orange-500 to-red-500",
      description: "اپلیکیشن سفارش آنلاین غذا با قابلیت ردیابی سفارش و پرداخت",
      technologies: ["React Native", "Firebase", "Stripe", "Google Maps"],
      link: "#"
    },
    {
      title: "پلتفرم آموزش آنلاین",
      category: "پلتفرم آموزشی",
      image: "bg-gradient-to-r from-green-500 to-emerald-500",
      description: "سیستم LMS کامل با قابلیت برگزاری کلاس آنلاین و مدیریت دوره‌ها",
      technologies: ["Next.js", "Express.js", "PostgreSQL", "WebRTC"],
      link: "#"
    },
    {
      title: "داشبورد تحلیل داده",
      category: "داشبورد",
      image: "bg-gradient-to-r from-purple-500 to-indigo-500",
      description: "داشبورد تحلیلی پیشرفته با نمودارهای تعاملی و گزارش‌گیری",
      technologies: ["Angular", "D3.js", "Python", "PostgreSQL"],
      link: "#"
    },
    {
      title: "سایت شرکتی معماری",
      category: "وبسایت شرکتی",
      image: "bg-gradient-to-r from-gray-600 to-gray-800",
      description: "وبسایت نمایشی شرکت معماری با گالری پروژه‌ها و فرم تماس",
      technologies: ["WordPress", "PHP", "MySQL", "GSAP"],
      link: "#"
    }
  ];

  const categories = ["همه", "وبسایت شرکتی", "فروشگاه آنلاین", "اپلیکیشن موبایل", "نرم‌افزار مدیریت", "پلتفرم آموزشی", "داشبورد"];

  return (
    <main className="bg-white min-h-screen">
      <nav className="bg-gray-900 text-white">
        <div className="container mx-auto flex items-center p-5 justify-between">
          <div>
            <Link href="/">
              <h1 className="text-2xl font-bold cursor-pointer">سپهروب</h1>
            </Link>
          </div>
          <ul className="flex items-center gap-8">
            <li>
              <Link href="/" className="hover:text-blue-400 transition-colors">صفحه اصلی</Link>
            </li>
            <li>
              <Link href="/blog" className="hover:text-blue-400 transition-colors">وبلاگ</Link>
            </li>
            <li>
              <Link href="/faq" className="hover:text-blue-400 transition-colors">پرسش و پاسخ</Link>
            </li>
            <li>
              <Link href="/portfolio" className="text-blue-400">نمونه کارها</Link>
            </li>
            <li>
              <Link href="/contact" className="hover:text-blue-400 transition-colors">تماس با ما</Link>
            </li>
            <li>
              <Link href="/about" className="hover:text-blue-400 transition-colors">درباره ما</Link>
            </li>
          </ul>
          <div>
            <Link href="/login" className="bg-blue-600 hover:bg-blue-500 transition-all px-4 py-1.5 text-sm text-white rounded-2xl">
              ورود / عضویت
            </Link>
          </div>
        </div>
      </nav>

      <div className="container mx-auto px-4 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">نمونه کارهای ما</h1>
          <p className="text-lg text-gray-600">مجموعه‌ای از پروژه‌های موفق که برای مشتریان خود انجام داده‌ایم</p>
        </div>

        {/* Filter Categories */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category}
              className={`px-4 py-2 rounded-full transition-colors ${
                category === "همه"
                  ? "bg-blue-600 text-white"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
            >
              {category}
            </button>
          ))}
        </div>

        {/* Projects Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {projects.map((project, index) => (
            <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow group">
              <div className={`h-48 ${project.image} relative overflow-hidden`}>
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                  <Link
                    href={project.link}
                    className="bg-white text-gray-900 px-4 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 hover:bg-gray-100"
                  >
                    مشاهده پروژه
                  </Link>
                </div>
              </div>
              <div className="p-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-blue-600 font-medium">{project.category}</span>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">{project.title}</h3>
                <p className="text-gray-600 mb-4">{project.description}</p>
                <div className="flex flex-wrap gap-2">
                  {project.technologies.map((tech) => (
                    <span
                      key={tech}
                      className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16 bg-gray-50 rounded-lg p-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">پروژه‌ای در ذهن دارید؟</h2>
          <p className="text-lg text-gray-600 mb-6">
            ما آماده‌ایم تا ایده شما را به واقعیت تبدیل کنیم
          </p>
          <div className="flex items-center justify-center gap-4">
            <Link
              href="/"
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg transition-colors"
            >
              شروع پروژه
            </Link>
            <Link
              href="/contact"
              className="border border-blue-600 text-blue-600 hover:bg-blue-50 px-8 py-3 rounded-lg transition-colors"
            >
              مشاوره رایگان
            </Link>
          </div>
        </div>
      </div>
    </main>
  );
}
