'use client';

import Link from "next/link";
import { useState } from "react";

export default function FAQ() {
  const [openItems, setOpenItems] = useState<number[]>([]);

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(item => item !== index)
        : [...prev, index]
    );
  };

  const faqs = [
    {
      question: "چه مدت زمان برای طراحی یک وبسایت نیاز است؟",
      answer: "زمان طراحی وبسایت بستگی به پیچیدگی و نیازهای پروژه دارد. یک وبسایت ساده معمولاً ۱-۲ هفته، وبسایت متوسط ۳-۴ هفته و پروژه‌های پیچیده ۶-۸ هفته زمان می‌برد."
    },
    {
      question: "هزینه طراحی وبسایت چقدر است؟",
      answer: "هزینه طراحی وبسایت بر اساس نوع پروژه، تعداد صفحات، قابلیت‌های مورد نیاز و پیچیدگی طراحی متفاوت است. برای دریافت قیمت دقیق، لطفاً با ما تماس بگیرید."
    },
    {
      question: "آیا وبسایت من ریسپانسیو خواهد بود؟",
      answer: "بله، تمام وبسایت‌هایی که ما طراحی می‌کنیم کاملاً ریسپانسیو هستند و روی تمام دستگاه‌ها شامل موبایل، تبلت و دسکتاپ به خوبی نمایش داده می‌شوند."
    },
    {
      question: "آیا پس از تحویل پروژه پشتیبانی ارائه می‌دهید؟",
      answer: "بله، ما ۶ ماه پشتیبانی رایگان برای تمام پروژه‌ها ارائه می‌دهیم. این پشتیبانی شامل رفع باگ‌ها، به‌روزرسانی‌های امنیتی و راهنمایی‌های فنی است."
    },
    {
      question: "آیا امکان تغییر در طراحی در حین پروژه وجود دارد؟",
      answer: "بله، تا ۳ بار تغییر در طراحی در هر مرحله امکان‌پذیر است. تغییرات بیشتر ممکن است هزینه اضافی داشته باشد."
    },
    {
      question: "آیا وبسایت من SEO شده خواهد بود؟",
      answer: "بله، تمام وبسایت‌های ما با رعایت اصول SEO طراحی می‌شوند. این شامل بهینه‌سازی سرعت، ساختار مناسب، متاتگ‌ها و سایر عوامل مهم SEO است."
    },
    {
      question: "آیا می‌توانم خودم محتوای وبسایت را مدیریت کنم؟",
      answer: "بله، ما از سیستم‌های مدیریت محتوای کاربرپسند استفاده می‌کنیم که به شما امکان به‌روزرسانی آسان محتوا، تصاویر و اخبار را می‌دهد."
    },
    {
      question: "آیا امکان ادغام با شبکه‌های اجتماعی وجود دارد؟",
      answer: "بله، وبسایت شما می‌تواند با تمام شبکه‌های اجتماعی مهم مانند اینستاگرام، تلگرام، واتساپ و... ادغام شود."
    }
  ];

  return (
    <main className="bg-white min-h-screen">
      <nav className="bg-gray-900 text-white">
        <div className="container mx-auto flex items-center p-5 justify-between">
          <div>
            <Link href="/">
              <h1 className="text-2xl font-bold cursor-pointer">سپهروب</h1>
            </Link>
          </div>
          <ul className="flex items-center gap-8">
            <li>
              <Link href="/" className="hover:text-blue-400 transition-colors">صفحه اصلی</Link>
            </li>
            <li>
              <Link href="/blog" className="hover:text-blue-400 transition-colors">وبلاگ</Link>
            </li>
            <li>
              <Link href="/faq" className="text-blue-400">پرسش و پاسخ</Link>
            </li>
            <li>
              <Link href="/portfolio" className="hover:text-blue-400 transition-colors">نمونه کارها</Link>
            </li>
            <li>
              <Link href="/contact" className="hover:text-blue-400 transition-colors">تماس با ما</Link>
            </li>
            <li>
              <Link href="/about" className="hover:text-blue-400 transition-colors">درباره ما</Link>
            </li>
          </ul>
          <div>
            <Link href="/login" className="bg-blue-600 hover:bg-blue-500 transition-all px-4 py-1.5 text-sm text-white rounded-2xl">
              ورود / عضویت
            </Link>
          </div>
        </div>
      </nav>

      <div className="container mx-auto px-4 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">پرسش‌های متداول</h1>
          <p className="text-lg text-gray-600">پاسخ سوالات رایج درباره خدمات ما</p>
        </div>

        <div className="max-w-3xl mx-auto">
          {faqs.map((faq, index) => (
            <div key={index} className="mb-4 border border-gray-200 rounded-lg overflow-hidden">
              <button
                onClick={() => toggleItem(index)}
                className="w-full px-6 py-4 text-right bg-gray-50 hover:bg-gray-100 transition-colors flex items-center justify-between"
              >
                <span className="font-medium text-gray-900">{faq.question}</span>
                <svg
                  className={`w-5 h-5 text-gray-500 transform transition-transform ${
                    openItems.includes(index) ? 'rotate-180' : ''
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </button>
              {openItems.includes(index) && (
                <div className="px-6 py-4 bg-white border-t border-gray-200">
                  <p className="text-gray-700 leading-relaxed">{faq.answer}</p>
                </div>
              )}
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">سوال دیگری دارید؟</h2>
          <p className="text-gray-600 mb-6">
            اگر پاسخ سوال خود را پیدا نکردید، می‌توانید مستقیماً با ما تماس بگیرید.
          </p>
          <div className="flex items-center justify-center gap-4">
            <Link
              href="/contact"
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
            >
              تماس با ما
            </Link>
            <Link
              href="tel:09397192230"
              className="border border-blue-600 text-blue-600 hover:bg-blue-50 px-6 py-3 rounded-lg transition-colors"
            >
              تماس تلفنی
            </Link>
          </div>
        </div>
      </div>
    </main>
  );
}
