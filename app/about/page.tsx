import Link from "next/link";

export default function About() {
  const team = [
    {
      name: "علی محمدی",
      role: "مدیر فنی و توسعه‌دهنده ارشد",
      image: "bg-gradient-to-r from-blue-500 to-purple-600",
      description: "بیش از ۸ سال تجربه در توسعه وب و موبایل"
    },
    {
      name: "سارا احمدی",
      role: "طراح UI/UX",
      image: "bg-gradient-to-r from-pink-500 to-rose-600",
      description: "متخصص طراحی رابط کاربری و تجربه کاربری"
    },
    {
      name: "محمد رضایی",
      role: "توسعه‌دهنده فرانت‌اند",
      image: "bg-gradient-to-r from-green-500 to-teal-600",
      description: "متخصص React، Vue و Angular"
    },
    {
      name: "فاطمه کریمی",
      role: "توسعه‌دهنده بک‌اند",
      image: "bg-gradient-to-r from-orange-500 to-red-600",
      description: "متخصص Node.js، Python و PHP"
    }
  ];

  const values = [
    {
      title: "کیفیت",
      description: "ما همیشه بر کیفیت بالای کار خود تأکید داریم",
      icon: "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
    },
    {
      title: "نوآوری",
      description: "استفاده از جدیدترین تکنولوژی‌ها و روش‌ها",
      icon: "M13 10V3L4 14h7v7l9-11h-7z"
    },
    {
      title: "پشتیبانی",
      description: "پشتیبانی ۲۴/۷ و خدمات پس از فروش",
      icon: "M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 2.25a9.75 9.75 0 100 19.5 9.75 9.75 0 000-19.5z"
    },
    {
      title: "سرعت",
      description: "تحویل به موقع پروژه‌ها با حفظ کیفیت",
      icon: "M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z"
    }
  ];

  return (
    <main className="bg-white min-h-screen">
      <nav className="bg-gray-900 text-white">
        <div className="container mx-auto flex items-center p-5 justify-between">
          <div>
            <Link href="/">
              <h1 className="text-2xl font-bold cursor-pointer">سپهروب</h1>
            </Link>
          </div>
          <ul className="flex items-center gap-8">
            <li>
              <Link href="/" className="hover:text-blue-400 transition-colors">صفحه اصلی</Link>
            </li>
            <li>
              <Link href="/blog" className="hover:text-blue-400 transition-colors">وبلاگ</Link>
            </li>
            <li>
              <Link href="/faq" className="hover:text-blue-400 transition-colors">پرسش و پاسخ</Link>
            </li>
            <li>
              <Link href="/portfolio" className="hover:text-blue-400 transition-colors">نمونه کارها</Link>
            </li>
            <li>
              <Link href="/contact" className="hover:text-blue-400 transition-colors">تماس با ما</Link>
            </li>
            <li>
              <Link href="/about" className="text-blue-400">درباره ما</Link>
            </li>
          </ul>
          <div>
            <Link href="/login" className="bg-blue-600 hover:bg-blue-500 transition-all px-4 py-1.5 text-sm text-white rounded-2xl">
              ورود / عضویت
            </Link>
          </div>
        </div>
      </nav>

      <div className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">درباره سپهروب</h1>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            ما تیمی از متخصصان با تجربه هستیم که با هدف ارائه بهترین خدمات طراحی وب و نرم‌افزار، 
            آماده همکاری با شما برای تحقق ایده‌هایتان هستیم.
          </p>
        </div>

        {/* Story Section */}
        <div className="grid lg:grid-cols-2 gap-12 mb-16">
          <div>
            <h2 className="text-3xl font-bold text-gray-900 mb-6">داستان ما</h2>
            <div className="space-y-4 text-gray-600">
              <p>
                سپهروب در سال ۱۳۹۸ با هدف ارائه خدمات با کیفیت در زمینه طراحی وب و توسعه نرم‌افزار تأسیس شد. 
                ما با شروع کوچک و تیمی متشکل از چند نفر، امروز به یکی از شرکت‌های معتبر در این حوزه تبدیل شده‌ایم.
              </p>
              <p>
                هدف ما همیشه این بوده که با استفاده از جدیدترین تکنولوژی‌ها و روش‌های نوین، 
                بهترین راه‌حل‌ها را برای نیازهای دیجیتال مشتریان خود ارائه دهیم.
              </p>
              <p>
                تا کنون بیش از ۲۰۰ پروژه موفق را به انجام رسانده‌ایم و افتخار همکاری با 
                شرکت‌ها و کسب‌وکارهای مختلف را داشته‌ایم.
              </p>
            </div>
          </div>
          <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg h-80 flex items-center justify-center">
            <div className="text-white text-center">
              <h3 className="text-2xl font-bold mb-2">۵+ سال تجربه</h3>
              <p>در خدمت مشتریان عزیز</p>
            </div>
          </div>
        </div>

        {/* Values Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">ارزش‌های ما</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <div key={index} className="text-center">
                <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={value.icon} />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">{value.title}</h3>
                <p className="text-gray-600">{value.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Team Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-gray-900 text-center mb-12">تیم ما</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <div key={index} className="text-center">
                <div className={`w-32 h-32 ${member.image} rounded-full mx-auto mb-4`}></div>
                <h3 className="text-xl font-bold text-gray-900 mb-1">{member.name}</h3>
                <p className="text-blue-600 font-medium mb-2">{member.role}</p>
                <p className="text-gray-600 text-sm">{member.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Stats Section */}
        <div className="bg-gray-50 rounded-lg p-8 mb-16">
          <div className="grid md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">۲۰۰+</div>
              <p className="text-gray-600">پروژه موفق</p>
            </div>
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">۱۵۰+</div>
              <p className="text-gray-600">مشتری راضی</p>
            </div>
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">۵+</div>
              <p className="text-gray-600">سال تجربه</p>
            </div>
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">۲۴/۷</div>
              <p className="text-gray-600">پشتیبانی</p>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">آماده همکاری با شما هستیم</h2>
          <p className="text-lg text-gray-600 mb-8">
            اگر پروژه‌ای در ذهن دارید، ما آماده‌ایم تا آن را به بهترین شکل ممکن پیاده‌سازی کنیم
          </p>
          <div className="flex items-center justify-center gap-4">
            <Link
              href="/"
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg transition-colors"
            >
              شروع پروژه
            </Link>
            <Link
              href="/contact"
              className="border border-blue-600 text-blue-600 hover:bg-blue-50 px-8 py-3 rounded-lg transition-colors"
            >
              تماس با ما
            </Link>
          </div>
        </div>
      </div>
    </main>
  );
}
