@import "tailwindcss";

/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.5); }
  50% { box-shadow: 0 0 40px rgba(59, 130, 246, 0.8); }
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* IRANSans Font Face Definitions */
@font-face {
  font-family: 'IRANSans';
  src: url('/fonts/IRANSansWeb.woff2') format('woff2'),
       url('/fonts/IRANSansWeb.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'IRANSans';
  src: url('/fonts/IRANSansWeb_Bold.woff2') format('woff2'),
       url('/fonts/IRANSansWeb_Bold.woff') format('woff');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'IRANSans';
  src: url('/fonts/IRANSansWeb_Light.woff2') format('woff2'),
       url('/fonts/IRANSansWeb_Light.woff') format('woff');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'IRANSans', var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'IRANSans', Arial, Helvetica, sans-serif;
}
