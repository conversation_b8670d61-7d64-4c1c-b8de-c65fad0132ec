import Link from "next/link";

export default function Blog() {
  return (
    <main className="bg-white min-h-screen">
      <nav className="bg-gray-900 text-white">
        <div className="container mx-auto flex items-center p-5 justify-between">
          <div>
            <Link href="/">
              <h1 className="text-2xl font-bold cursor-pointer">سپهروب</h1>
            </Link>
          </div>
          <ul className="flex items-center gap-8">
            <li>
              <Link href="/" className="hover:text-blue-400 transition-colors">صفحه اصلی</Link>
            </li>
            <li>
              <Link href="/blog" className="text-blue-400">وبلاگ</Link>
            </li>
            <li>
              <Link href="/faq" className="hover:text-blue-400 transition-colors">پرسش و پاسخ</Link>
            </li>
            <li>
              <Link href="/portfolio" className="hover:text-blue-400 transition-colors">نمونه کارها</Link>
            </li>
            <li>
              <Link href="/contact" className="hover:text-blue-400 transition-colors">تماس با ما</Link>
            </li>
            <li>
              <Link href="/about" className="hover:text-blue-400 transition-colors">درباره ما</Link>
            </li>
          </ul>
          <div>
            <Link href="/login" className="bg-blue-600 hover:bg-blue-500 transition-all px-4 py-1.5 text-sm text-white rounded-2xl">
              ورود / عضویت
            </Link>
          </div>
        </div>
      </nav>

      <div className="container mx-auto px-4 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">وبلاگ سپهروب</h1>
          <p className="text-lg text-gray-600">آخرین مقالات و اخبار دنیای طراحی وب و نرم‌افزار</p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Blog Post 1 */}
          <article className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
            <div className="h-48 bg-gradient-to-r from-blue-500 to-purple-600"></div>
            <div className="p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-2">
                راهنمای کامل طراحی وبسایت ریسپانسیو
              </h2>
              <p className="text-gray-600 mb-4">
                در این مقاله به بررسی اصول و تکنیک‌های طراحی وبسایت‌های ریسپانسیو می‌پردازیم...
              </p>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">۱۵ آذر ۱۴۰۲</span>
                <Link href="/blog/responsive-design" className="text-blue-600 hover:text-blue-800 font-medium">
                  ادامه مطلب
                </Link>
              </div>
            </div>
          </article>

          {/* Blog Post 2 */}
          <article className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
            <div className="h-48 bg-gradient-to-r from-green-500 to-blue-600"></div>
            <div className="p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-2">
                بهترین فریمورک‌های جاوااسکریپت در سال ۲۰۲۴
              </h2>
              <p className="text-gray-600 mb-4">
                مقایسه کامل React، Vue و Angular و انتخاب بهترین گزینه برای پروژه شما...
              </p>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">۱۰ آذر ۱۴۰۲</span>
                <Link href="/blog/javascript-frameworks" className="text-blue-600 hover:text-blue-800 font-medium">
                  ادامه مطلب
                </Link>
              </div>
            </div>
          </article>

          {/* Blog Post 3 */}
          <article className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
            <div className="h-48 bg-gradient-to-r from-purple-500 to-pink-600"></div>
            <div className="p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-2">
                اصول UI/UX در طراحی اپلیکیشن موبایل
              </h2>
              <p className="text-gray-600 mb-4">
                نکات مهم و اصول طراحی رابط کاربری برای اپلیکیشن‌های موبایل موفق...
              </p>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">۵ آذر ۱۴۰۲</span>
                <Link href="/blog/mobile-ui-ux" className="text-blue-600 hover:text-blue-800 font-medium">
                  ادامه مطلب
                </Link>
              </div>
            </div>
          </article>

          {/* Blog Post 4 */}
          <article className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
            <div className="h-48 bg-gradient-to-r from-orange-500 to-red-600"></div>
            <div className="p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-2">
                امنیت وبسایت: راهکارهای محافظت از داده‌ها
              </h2>
              <p className="text-gray-600 mb-4">
                روش‌های مختلف برای افزایش امنیت وبسایت و محافظت از اطلاعات کاربران...
              </p>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">۱ آذر ۱۴۰۲</span>
                <Link href="/blog/website-security" className="text-blue-600 hover:text-blue-800 font-medium">
                  ادامه مطلب
                </Link>
              </div>
            </div>
          </article>

          {/* Blog Post 5 */}
          <article className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
            <div className="h-48 bg-gradient-to-r from-teal-500 to-green-600"></div>
            <div className="p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-2">
                بهینه‌سازی سرعت وبسایت برای SEO
              </h2>
              <p className="text-gray-600 mb-4">
                تکنیک‌های کاربردی برای افزایش سرعت بارگذاری وبسایت و بهبود رتبه در گوگل...
              </p>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">۲۵ آبان ۱۴۰۲</span>
                <Link href="/blog/website-speed-optimization" className="text-blue-600 hover:text-blue-800 font-medium">
                  ادامه مطلب
                </Link>
              </div>
            </div>
          </article>

          {/* Blog Post 6 */}
          <article className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
            <div className="h-48 bg-gradient-to-r from-indigo-500 to-purple-600"></div>
            <div className="p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-2">
                آینده هوش مصنوعی در توسعه نرم‌افزار
              </h2>
              <p className="text-gray-600 mb-4">
                بررسی تأثیر هوش مصنوعی بر صنعت نرم‌افزار و تغییرات آینده...
              </p>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-500">۲۰ آبان ۱۴۰۲</span>
                <Link href="/blog/ai-in-software-development" className="text-blue-600 hover:text-blue-800 font-medium">
                  ادامه مطلب
                </Link>
              </div>
            </div>
          </article>
        </div>

        {/* Pagination */}
        <div className="flex justify-center mt-12">
          <div className="flex items-center gap-2">
            <button className="px-3 py-2 text-gray-500 hover:text-gray-700">قبلی</button>
            <button className="px-3 py-2 bg-blue-600 text-white rounded">۱</button>
            <button className="px-3 py-2 text-gray-700 hover:text-gray-900">۲</button>
            <button className="px-3 py-2 text-gray-700 hover:text-gray-900">۳</button>
            <button className="px-3 py-2 text-gray-500 hover:text-gray-700">بعدی</button>
          </div>
        </div>
      </div>
    </main>
  );
}
