import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON>o } from "next/font/google";
import "./globals.css";

export const metadata: Metadata = {
  title: "سپهروب",
  description: "طراحی وبسایت و نرم افزار اختصاصی",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="fa" dir="rtl">
      
      <body>
        {children}
      </body>
    </html>
  );
}
