import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    const { fullName, phone, requestType, description } = body;

    if (!fullName || !phone || !requestType || !description) {
      return NextResponse.json(
        { error: 'تمام فیلدها الزامی هستند' },
        { status: 400 }
      );
    }

    // Validate phone number format
    if (!/^09\d{9}$/.test(phone)) {
      return NextResponse.json(
        { error: 'شماره تماس معتبر نیست' },
        { status: 400 }
      );
    }

    // Validate description length
    if (description.length > 2000) {
      return NextResponse.json(
        { error: 'توضیحات نمی‌تواند بیش از 2000 کارکتر باشد' },
        { status: 400 }
      );
    }

    // Here you would typically save to a database
    // For now, we'll just log the data and return success
    console.log('New order received:', {
      fullName,
      phone,
      requestType,
      description,
      timestamp: new Date().toISOString()
    });

    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 1000));

    return NextResponse.json(
      { 
        message: 'درخواست شما با موفقیت ثبت شد',
        orderId: Math.random().toString(36).substr(2, 9)
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error processing order:', error);
    return NextResponse.json(
      { error: 'خطا در پردازش درخواست' },
      { status: 500 }
    );
  }
}
