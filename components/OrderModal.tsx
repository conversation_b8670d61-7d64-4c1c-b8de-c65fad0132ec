'use client';

import { useState } from 'react';

interface OrderModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface FormData {
  fullName: string;
  phone: string;
  requestType: string;
  description: string;
}

interface FormErrors {
  fullName?: string;
  phone?: string;
  requestType?: string;
  description?: string;
}

const OrderModal: React.FC<OrderModalProps> = ({ isOpen, onClose }) => {
  const [formData, setFormData] = useState<FormData>({
    fullName: '',
    phone: '',
    requestType: '',
    description: ''
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [isClosing, setIsClosing] = useState(false);

  const requestTypes = [
    { value: 'website', label: '🌐 طراحی وبسایت', description: 'وبسایت ریسپانسیو و مدرن' },
    { value: 'mobile', label: '📱 اپلیکیشن موبایل', description: 'اندروید و iOS' },
    { value: 'desktop', label: '💻 نرم‌افزار دسکتاپ', description: 'ویندوز، مک و لینوکس' },
    { value: 'uiux', label: '🎨 طراحی UI/UX', description: 'رابط کاربری زیبا' },
    { value: 'ecommerce', label: '🛒 فروشگاه آنلاین', description: 'با درگاه پرداخت' },
    { value: 'consultation', label: '💡 مشاوره فنی', description: 'راهنمایی تخصصی' },
    { value: 'other', label: '⚙️ سایر', description: 'سایر خدمات' }
  ];

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.fullName.trim()) {
      newErrors.fullName = 'نام و نام خانوادگی الزامی است';
    } else if (formData.fullName.trim().split(' ').length < 2) {
      newErrors.fullName = 'لطفاً نام و نام خانوادگی را وارد کنید';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'شماره تماس الزامی است';
    } else if (!/^09\d{9}$/.test(formData.phone)) {
      newErrors.phone = 'شماره تماس معتبر نیست';
    }

    if (!formData.requestType) {
      newErrors.requestType = 'نوع درخواست الزامی است';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'توضیحات الزامی است';
    } else if (formData.description.length > 2000) {
      newErrors.description = 'توضیحات نمی‌تواند بیش از 2000 کارکتر باشد';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call
      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        // Show success toast
        setShowToast(true);
        
        // Reset form
        setFormData({
          fullName: '',
          phone: '',
          requestType: '',
          description: ''
        });

        // Hide toast after 3 seconds
        setTimeout(() => {
          setShowToast(false);
          onClose();
        }, 3000);
      } else {
        throw new Error('خطا در ارسال درخواست');
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      alert('خطا در ارسال درخواست. لطفاً دوباره تلاش کنید.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      setFormData({
        firstName: '',
        lastName: '',
        phone: '',
        requestType: '',
        description: ''
      });
      setErrors({});
      setIsClosing(false);
      onClose();
    }, 200);
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className={`fixed inset-0 bg-gray-900/50 backdrop-blur-md z-40 transition-all duration-300 ease-out ${
          isOpen && !isClosing ? 'bg-opacity-50' : 'bg-opacity-0'
        }`}
        onClick={handleClose}
      />

      {/* Modal */}
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <div className={`bg-white rounded-2xl shadow-2xl max-w-lg w-full max-h-[90vh] overflow-y-auto transform transition-all duration-300 ease-out ${
          isOpen && !isClosing ? 'scale-100 opacity-100 translate-y-0' : 'scale-95 opacity-0 translate-y-4'
        }`}>
          {/* Header */}
 
          {/* Form */}
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Full Name Field */}
            <div>
              <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-2">
                <span className="flex items-center gap-2">
                  👤 نام و نام خانوادگی *
                </span>
              </label>
              <input
                type="text"
                id="fullName"
                name="fullName"
                value={formData.fullName}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all ${
                  errors.fullName ? 'border-red-500 bg-red-50' : 'border-gray-300 focus:border-blue-500'
                }`}
                placeholder="مثال: علی احمدی"
              />
              {errors.fullName && (
                <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                  <span>⚠️</span> {errors.fullName}
                </p>
              )}
            </div>

            {/* Phone */}
            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                <span className="flex items-center gap-2">
                  📞 شماره تماس *
                </span>
              </label>
              <div className="relative">
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all ${
                    errors.phone ? 'border-red-500 bg-red-50' : 'border-gray-300 focus:border-blue-500'
                  }`}
                  placeholder="09123456789"
                  maxLength={11}
                  dir="ltr"
                />
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                  🇮🇷
                </div>
              </div>
              {errors.phone && (
                <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                  <span>⚠️</span> {errors.phone}
                </p>
              )}
            </div>

            {/* Request Type */}
            <div>
              <label htmlFor="requestType" className="block text-sm font-medium text-gray-700 mb-2">
                <span className="flex items-center gap-2">
                  🎯 نوع درخواست *
                </span>
              </label>
              <select
                id="requestType"
                name="requestType"
                value={formData.requestType}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all appearance-none bg-white ${
                  errors.requestType ? 'border-red-500 bg-red-50' : 'border-gray-300 focus:border-blue-500'
                }`}
                style={{ backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`, backgroundPosition: 'left 0.75rem center', backgroundRepeat: 'no-repeat', backgroundSize: '1.5em 1.5em' }}
              >
                <option value="">🔍 انتخاب کنید</option>
                {requestTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
              {errors.requestType && (
                <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                  <span>⚠️</span> {errors.requestType}
                </p>
              )}
              {formData.requestType && (
                <div className="mt-2 p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-700">
                    {requestTypes.find(type => type.value === formData.requestType)?.description}
                  </p>
                </div>
              )}
            </div>

            {/* Description */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                <span className="flex items-center gap-2">
                  📝 توضیحات پروژه *
                </span>
              </label>
              <div className="relative">
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={5}
                  maxLength={2000}
                  className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none transition-all ${
                    errors.description ? 'border-red-500 bg-red-50' : 'border-gray-300 focus:border-blue-500'
                  }`}
                  placeholder="لطفاً توضیحات کاملی از پروژه خود ارائه دهید:
• هدف پروژه چیست؟
• چه ویژگی‌هایی نیاز دارید؟
• زمان‌بندی مورد نظر
• بودجه تقریبی (اختیاری)"
                />
                <div className="absolute bottom-3 left-3">
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    formData.description.length > 1800 ? 'bg-red-100 text-red-600' :
                    formData.description.length > 1500 ? 'bg-yellow-100 text-yellow-600' :
                    'bg-gray-100 text-gray-500'
                  }`}>
                    {formData.description.length}/2000
                  </span>
                </div>
              </div>
              {errors.description && (
                <p className="text-red-500 text-xs mt-1 flex items-center gap-1">
                  <span>⚠️</span> {errors.description}
                </p>
              )}
              <div className="mt-2 text-xs text-gray-500">
                💡 نکته: هرچه توضیحات شما کاملتر باشد، پیشنهاد بهتری دریافت خواهید کرد
              </div>
            </div>

            {/* Submit Button */}
            <div className="pt-6 border-t border-gray-100">
              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-medium py-4 px-6 rounded-xl transition-all duration-300 transform  disabled:scale-100 shadow-lg hover:shadow-xl"
              >
                {isSubmitting ? (
                  <span className="flex items-center justify-center gap-2">
                    <svg className="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    در حال ارسال...
                  </span>
                ) : (
                  <span className="flex items-center justify-center gap-2">
                    🚀 ارسال درخواست
                  </span>
                )}
              </button>
              <p className="text-xs text-gray-500 text-center mt-3">
                با ارسال درخواست، تیم ما در کمترین زمان با شما تماس خواهد گرفت
              </p>
            </div>
          </form>
        </div>
      </div>

      {/* Toast Notification */}
      {showToast && (
        <div className={`fixed top-4 right-4 z-60 transform transition-all duration-500 ${
          showToast ? 'translate-y-0 opacity-100 scale-100' : '-translate-y-2 opacity-0 scale-95'
        }`}>
          <div className="bg-gradient-to-r from-green-500 to-emerald-500 text-white px-6 py-4 rounded-2xl shadow-2xl">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <div>
                <div className="font-bold">✅ موفقیت آمیز!</div>
                <div className="text-sm text-green-100">درخواست شما ارسال شد و به زودی تماس می‌گیریم</div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default OrderModal;
